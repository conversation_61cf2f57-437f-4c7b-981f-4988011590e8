version: '3.8'

services:
  # ================================
  # Shell Application (Development)
  # ================================
  shell-dev:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: app-front-shell-dev
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - VITE_AUTH_URL=http://localhost:3001
      - VITE_DASHBOARD_URL=http://localhost:3002
      - VITE_PROFILE_URL=http://localhost:3003
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    volumes:
      - ..:/app
      - /app/node_modules
      - node_modules_cache:/app/node_modules
    networks:
      - app-front-dev-network
    command: ["sh", "-c", "cd shell && npm run dev -- --host 0.0.0.0"]
    restart: unless-stopped
    stdin_open: true
    tty: true

  # ================================
  # Auth Micro Frontend (Development)
  # ================================
  auth-dev:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: app-front-auth-dev
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - VITE_SHELL_URL=http://localhost:3000
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
    volumes:
      - ..:/app
      - /app/node_modules
      - node_modules_cache:/app/node_modules
    networks:
      - app-front-dev-network
    command: ["sh", "-c", "cd microfrontends/auth && npm run dev -- --host 0.0.0.0"]
    restart: unless-stopped
    stdin_open: true
    tty: true

  # ================================
  # Dashboard Micro Frontend (Development)
  # ================================
  dashboard-dev:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: app-front-dashboard-dev
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - VITE_SHELL_URL=http://localhost:3000
    volumes:
      - ..:/app
      - /app/node_modules
      - node_modules_cache:/app/node_modules
    networks:
      - app-front-dev-network
    command: ["sh", "-c", "cd microfrontends/dashboard && npm run dev || echo 'Dashboard not implemented yet'"]
    restart: unless-stopped

  # ================================
  # Profile Micro Frontend (Development)
  # ================================
  profile-dev:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: app-front-profile-dev
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - VITE_SHELL_URL=http://localhost:3000
    volumes:
      - ..:/app
      - /app/node_modules
      - node_modules_cache:/app/node_modules
    networks:
      - app-front-dev-network
    command: ["sh", "-c", "cd microfrontends/profile && npm run dev || echo 'Profile not implemented yet'"]
    restart: unless-stopped

  # ================================
  # Hot Reload File Watcher
  # ================================
  file-watcher:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: app-front-file-watcher
    volumes:
      - ..:/app
      - /app/node_modules
    networks:
      - app-front-dev-network
    command: ["sh", "-c", "while true; do echo 'File watcher running...'; sleep 30; done"]
    restart: unless-stopped

networks:
  app-front-dev-network:
    driver: bridge
    name: app-front-dev-network

volumes:
  node_modules_cache:
    driver: local
