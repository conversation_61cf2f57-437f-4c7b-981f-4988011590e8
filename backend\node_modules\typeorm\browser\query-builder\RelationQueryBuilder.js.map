{"version": 3, "sources": ["../browser/src/query-builder/RelationQueryBuilder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAC7C,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAA;AACnD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAA;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AACvC,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AAGjD;;;;GAIG;AACH,MAAM,OAAO,oBAEX,SAAQ,YAAoB;IAF9B;;QAGa,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;IA4L/D,CAAC;IA1LG,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAE5E;;OAEG;IACH,QAAQ;QACJ,OAAO,EAAE,CAAA;IACb,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,EAAE,CAAC,MAAmB;QAClB,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG,MAAM,CAAA;QAC9B,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,GAAG,CAAC,KAAU;QAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAA;QAEpD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YACtB,gEAAgE;YAChE,MAAM,IAAI,YAAY,CAClB,4GAA4G,CAC/G,CAAA;QAEL,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,WAAW;YAC7C,MAAM,IAAI,YAAY,CAClB,4EAA4E;gBACxE,kBAAkB,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,YAAY,aAAa;gBAClF,4BAA4B,CACnC,CAAA;QAEL,+GAA+G;QAC/G,IACI,QAAQ,CAAC,WAAW;YACpB,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;YAC/B,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACzB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC;YAE5D,MAAM,IAAI,YAAY,CAClB,2HAA2H,CAC9H,CAAA;QAEL,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;QAC7D,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAChC,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,GAAG,CAAC,KAAkB;QACxB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAM;QAEtD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAA;QAEpD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YACtB,gEAAgE;YAChE,MAAM,IAAI,YAAY,CAClB,4GAA4G,CAC/G,CAAA;QAEL,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,UAAU;YAC3C,MAAM,IAAI,YAAY,CAClB,8EAA8E;gBAC1E,kBAAkB,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,YAAY,aAAa;gBAClF,4BAA4B,CACnC,CAAA;QAEL,+GAA+G;QAC/G,IACI,QAAQ,CAAC,WAAW;YACpB,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;YAC/B,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACzB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC;YAE5D,MAAM,IAAI,YAAY,CAClB,2HAA2H,CAC9H,CAAA;QAEL,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;QAC7D,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAChC,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,MAAM,CAAC,KAAkB;QAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAM;QAEtD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAA;QAEpD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YACtB,gEAAgE;YAChE,MAAM,IAAI,YAAY,CAClB,4GAA4G,CAC/G,CAAA;QAEL,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,UAAU;YAC3C,MAAM,IAAI,YAAY,CAClB,8EAA8E;gBAC1E,kBAAkB,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,YAAY,aAAa;gBAClF,gCAAgC,CACvC,CAAA;QAEL,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;QAC7D,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAChC,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,YAAY,CACd,KAAkB,EAClB,OAAoB;QAEpB,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAC1B,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAED;;;;OAIG;IAEH;;;;OAIG;IAEH;;;OAGG;IACH,KAAK,CAAC,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,EAAK,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;IAC3D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,QAAQ;QACV,IAAI,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAA;QAC9B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAA;YACvD,IAAI,QAAQ,CAAC,sBAAsB;gBAC/B,MAAM,IAAI,YAAY,CAClB,8GAA8G,CACjH,CAAA;YAEL,EAAE,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA;QACtD,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,IAAI,CACtC,IAAI,CAAC,aAAa,CAAC,gBAAgB,EACnC,EAAE,EACF,IAAI,CAAC,WAAW,CACnB,CAAA;IACL,CAAC;CACJ", "file": "RelationQueryBuilder.js", "sourcesContent": ["import { QueryBuilder } from \"./QueryBuilder\"\nimport { RelationUpdater } from \"./RelationUpdater\"\nimport { RelationRemover } from \"./RelationRemover\"\nimport { TypeORMError } from \"../error\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\n\n/**\n * Allows to work with entity relations and perform specific operations with those relations.\n *\n * todo: add transactions everywhere\n */\nexport class RelationQueryBuilder<\n    Entity extends ObjectLiteral,\n> extends QueryBuilder<Entity> {\n    readonly \"@instanceof\" = Symbol.for(\"RelationQueryBuilder\")\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets generated SQL query without parameters being replaced.\n     */\n    getQuery(): string {\n        return \"\"\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Sets entity (target) which relations will be updated.\n     */\n    of(entity: any | any[]): this {\n        this.expressionMap.of = entity\n        return this\n    }\n\n    /**\n     * Sets entity relation's value.\n     * Value can be entity, entity id or entity id map (if entity has composite ids).\n     * Works only for many-to-one and one-to-one relations.\n     * For many-to-many and one-to-many relations use #add and #remove methods instead.\n     */\n    async set(value: any): Promise<void> {\n        const relation = this.expressionMap.relationMetadata\n\n        if (!this.expressionMap.of)\n            // todo: move this check before relation query builder creation?\n            throw new TypeORMError(\n                `Entity whose relation needs to be set is not set. Use .of method to define whose relation you want to set.`,\n            )\n\n        if (relation.isManyToMany || relation.isOneToMany)\n            throw new TypeORMError(\n                `Set operation is only supported for many-to-one and one-to-one relations. ` +\n                    `However given \"${relation.propertyPath}\" has ${relation.relationType} relation. ` +\n                    `Use .add() method instead.`,\n            )\n\n        // if there are multiple join columns then user must send id map as \"value\" argument. check if he really did it\n        if (\n            relation.joinColumns &&\n            relation.joinColumns.length > 1 &&\n            (!ObjectUtils.isObject(value) ||\n                Object.keys(value).length < relation.joinColumns.length)\n        )\n            throw new TypeORMError(\n                `Value to be set into the relation must be a map of relation ids, for example: .set({ firstName: \"...\", lastName: \"...\" })`,\n            )\n\n        const updater = new RelationUpdater(this, this.expressionMap)\n        return updater.update(value)\n    }\n\n    /**\n     * Adds (binds) given value to entity relation.\n     * Value can be entity, entity id or entity id map (if entity has composite ids).\n     * Value also can be array of entities, array of entity ids or array of entity id maps (if entity has composite ids).\n     * Works only for many-to-many and one-to-many relations.\n     * For many-to-one and one-to-one use #set method instead.\n     */\n    async add(value: any | any[]): Promise<void> {\n        if (Array.isArray(value) && value.length === 0) return\n\n        const relation = this.expressionMap.relationMetadata\n\n        if (!this.expressionMap.of)\n            // todo: move this check before relation query builder creation?\n            throw new TypeORMError(\n                `Entity whose relation needs to be set is not set. Use .of method to define whose relation you want to set.`,\n            )\n\n        if (relation.isManyToOne || relation.isOneToOne)\n            throw new TypeORMError(\n                `Add operation is only supported for many-to-many and one-to-many relations. ` +\n                    `However given \"${relation.propertyPath}\" has ${relation.relationType} relation. ` +\n                    `Use .set() method instead.`,\n            )\n\n        // if there are multiple join columns then user must send id map as \"value\" argument. check if he really did it\n        if (\n            relation.joinColumns &&\n            relation.joinColumns.length > 1 &&\n            (!ObjectUtils.isObject(value) ||\n                Object.keys(value).length < relation.joinColumns.length)\n        )\n            throw new TypeORMError(\n                `Value to be set into the relation must be a map of relation ids, for example: .set({ firstName: \"...\", lastName: \"...\" })`,\n            )\n\n        const updater = new RelationUpdater(this, this.expressionMap)\n        return updater.update(value)\n    }\n\n    /**\n     * Removes (unbinds) given value from entity relation.\n     * Value can be entity, entity id or entity id map (if entity has composite ids).\n     * Value also can be array of entities, array of entity ids or array of entity id maps (if entity has composite ids).\n     * Works only for many-to-many and one-to-many relations.\n     * For many-to-one and one-to-one use #set method instead.\n     */\n    async remove(value: any | any[]): Promise<void> {\n        if (Array.isArray(value) && value.length === 0) return\n\n        const relation = this.expressionMap.relationMetadata\n\n        if (!this.expressionMap.of)\n            // todo: move this check before relation query builder creation?\n            throw new TypeORMError(\n                `Entity whose relation needs to be set is not set. Use .of method to define whose relation you want to set.`,\n            )\n\n        if (relation.isManyToOne || relation.isOneToOne)\n            throw new TypeORMError(\n                `Add operation is only supported for many-to-many and one-to-many relations. ` +\n                    `However given \"${relation.propertyPath}\" has ${relation.relationType} relation. ` +\n                    `Use .set(null) method instead.`,\n            )\n\n        const remover = new RelationRemover(this, this.expressionMap)\n        return remover.remove(value)\n    }\n\n    /**\n     * Adds (binds) and removes (unbinds) given values to/from entity relation.\n     * Value can be entity, entity id or entity id map (if entity has composite ids).\n     * Value also can be array of entities, array of entity ids or array of entity id maps (if entity has composite ids).\n     * Works only for many-to-many and one-to-many relations.\n     * For many-to-one and one-to-one use #set method instead.\n     */\n    async addAndRemove(\n        added: any | any[],\n        removed: any | any[],\n    ): Promise<void> {\n        await this.remove(removed)\n        await this.add(added)\n    }\n\n    /**\n     * Gets entity's relation id.\n    async getId(): Promise<any> {\n\n    }*/\n\n    /**\n     * Gets entity's relation ids.\n    async getIds(): Promise<any[]> {\n        return [];\n    }*/\n\n    /**\n     * Loads a single entity (relational) from the relation.\n     * You can also provide id of relational entity to filter by.\n     */\n    async loadOne<T = any>(): Promise<T | undefined> {\n        return this.loadMany<T>().then((results) => results[0])\n    }\n\n    /**\n     * Loads many entities (relational) from the relation.\n     * You can also provide ids of relational entities to filter by.\n     */\n    async loadMany<T = any>(): Promise<T[]> {\n        let of = this.expressionMap.of\n        if (!ObjectUtils.isObject(of)) {\n            const metadata = this.expressionMap.mainAlias!.metadata\n            if (metadata.hasMultiplePrimaryKeys)\n                throw new TypeORMError(\n                    `Cannot load entity because only one primary key was specified, however entity contains multiple primary keys`,\n                )\n\n            of = metadata.primaryColumns[0].createValueMap(of)\n        }\n\n        return this.connection.relationLoader.load(\n            this.expressionMap.relationMetadata,\n            of,\n            this.queryRunner,\n        )\n    }\n}\n"], "sourceRoot": ".."}