import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON>, EventBus } from '@nestjs/cqrs';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { LoginUserCommand } from '../login-user.command';
import { User, UserDocument } from '../../schemas/user.schema';
import { RefreshToken, RefreshTokenDocument } from '../../schemas/refresh-token.schema';
import { UserLoggedInEvent } from '../../events/user-logged-in.event';

@CommandHandler(LoginUserCommand)
@Injectable()
export class LoginUserHandler implements ICommandHandler<LoginUserCommand> {
  constructor(
    @InjectModel(User.name, 'write')
    private userWriteModel: Model<UserDocument>,
    @InjectModel(User.name, 'read')
    private userReadModel: Model<UserDocument>,
    @InjectModel(RefreshToken.name, 'write')
    private refreshTokenModel: Model<RefreshTokenDocument>,
    private eventBus: EventBus,
  ) {}

  async execute(command: LoginUserCommand): Promise<{ user: UserDocument; refreshToken: RefreshTokenDocument }> {
    const { loginDto, userAgent, ipAddress } = command;

    // Find user
    const user = await this.userReadModel.findOne({ email: loginDto.email }).exec();
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(loginDto.password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if user is active
    if (!user.isActive()) {
      throw new UnauthorizedException('Account is not active or email not verified');
    }

    // Update last login time
    await this.userWriteModel.findByIdAndUpdate(
      user._id,
      { lastLoginAt: new Date() },
      { new: true }
    ).exec();

    // Generate refresh token
    const refreshToken = await this.generateRefreshToken(user, userAgent, ipAddress);

    // Publish domain event
    const event = new UserLoggedInEvent(
      user._id.toString(),
      user.email,
      ipAddress,
      userAgent,
      new Date(),
    );

    this.eventBus.publish(event);

    return { user, refreshToken };
  }

  private async generateRefreshToken(
    user: UserDocument,
    userAgent?: string,
    ipAddress?: string,
  ): Promise<RefreshTokenDocument> {
    const token = uuidv4();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    const refreshToken = new this.refreshTokenModel({
      token,
      userId: user._id,
      expiresAt,
      userAgent,
      ipAddress,
    });

    return refreshToken.save();
  }
}
