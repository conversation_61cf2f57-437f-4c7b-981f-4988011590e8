{"version": 3, "sources": ["../browser/src/decorator/tree/TreeParent.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AAKtD;;;GAGG;AACH,MAAM,UAAU,UAAU,CAAC,OAE1B;IACG,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,EAAqB,CAAA;QAE7C,4CAA4C;QAC5C,MAAM,aAAa,GACf,OAAO,IAAK,OAAe,CAAC,WAAW;YACnC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,CAAC;YAC1D,CAAC,CAAC,SAAS,CAAA;QACnB,MAAM,MAAM,GACR,CAAC,aAAa;YACV,OAAO,aAAa,CAAC,IAAI,KAAK,QAAQ;YACtC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC;YACnD,KAAK,CAAA;QAET,sBAAsB,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;YACpC,YAAY,EAAE,IAAI;YAClB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,aAAa;YAC3B,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,WAAW;YAC9B,OAAO,EAAE,OAAO;SACK,CAAC,CAAA;IAC9B,CAAC,CAAA;AACL,CAAC", "file": "TreeParent.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { RelationMetadataArgs } from \"../../metadata-args/RelationMetadataArgs\"\nimport { OnDeleteType } from \"../../metadata/types/OnDeleteType\"\nimport { RelationOptions } from \"../options/RelationOptions\"\n\n/**\n * Marks a entity property as a parent of the tree.\n * \"Tree parent\" indicates who owns (is a parent) of this entity in tree structure.\n */\nexport function TreeParent(options?: {\n    onDelete?: OnDeleteType\n}): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        if (!options) options = {} as RelationOptions\n\n        // now try to determine it its lazy relation\n        const reflectedType =\n            Reflect && (Reflect as any).getMetadata\n                ? Reflect.getMetadata(\"design:type\", object, propertyName)\n                : undefined\n        const isLazy =\n            (reflectedType &&\n                typeof reflectedType.name === \"string\" &&\n                reflectedType.name.toLowerCase() === \"promise\") ||\n            false\n\n        getMetadataArgsStorage().relations.push({\n            isTreeParent: true,\n            target: object.constructor,\n            propertyName: propertyName,\n            isLazy: isLazy,\n            relationType: \"many-to-one\",\n            type: () => object.constructor,\n            options: options,\n        } as RelationMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}