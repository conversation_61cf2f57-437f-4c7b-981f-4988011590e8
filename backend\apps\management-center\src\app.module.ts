import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { CqrsModule } from '@nestjs/cqrs';
import { EventStoreModule } from '@enterprise/event-store';
import { EventBusModule } from '@enterprise/event-bus';
import { UsersModule } from './users/users.module';
import { OrganizationsModule } from './organizations/organizations.module';
import { SettingsModule } from './settings/settings.module';
import { HealthModule } from './health/health.module';
import { AuthModule } from './auth/auth.module';

@Module({
  imports: [
    // MongoDB Write Database (Primary)
    MongooseModule.forRoot(
      process.env.MONGODB_WRITE_URI || 'mongodb://localhost:27017/management_center_write',
      {
        connectionName: 'write',
      }
    ),
    // MongoDB Read Database (Replica/Secondary)
    MongooseModule.forRoot(
      process.env.MONGODB_READ_URI || 'mongodb://localhost:27017/management_center_read',
      {
        connectionName: 'read',
      }
    ),
    // Keep PostgreSQL for legacy data if needed
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 5432,
      username: process.env.DB_USERNAME || 'postgres',
      password: process.env.DB_PASSWORD || 'password',
      database: process.env.DB_NAME || 'management_center',
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: process.env.NODE_ENV !== 'production',
      logging: process.env.NODE_ENV === 'development',
    }),
    CqrsModule,
    EventStoreModule.forRoot({
      connectionString: process.env.EVENTSTORE_CONNECTION_STRING,
    }),
    EventBusModule.forRoot({
      url: process.env.RABBITMQ_URL || 'amqp://admin:password@localhost:5672',
      exchange: process.env.RABBITMQ_EXCHANGE || 'enterprise.events',
      serviceName: 'management-center',
    }),
    AuthModule,
    UsersModule,
    OrganizationsModule,
    SettingsModule,
    HealthModule,
  ],
})
export class AppModule {}
