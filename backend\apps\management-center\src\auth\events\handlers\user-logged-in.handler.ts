import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { Injectable, Logger } from '@nestjs/common';
import { EventBusService } from '@enterprise/event-bus';
import { EventStoreService } from '@enterprise/event-store';
import { UserLoggedInEvent } from '../user-logged-in.event';

@EventsHandler(UserLoggedInEvent)
@Injectable()
export class UserLoggedInHandler implements IEventHandler<UserLoggedInEvent> {
  private readonly logger = new Logger(UserLoggedInHandler.name);

  constructor(
    private readonly eventBusService: EventBusService,
    private readonly eventStoreService: EventStoreService,
  ) {}

  async handle(event: UserLoggedInEvent) {
    this.logger.log(`Handling UserLoggedInEvent for user: ${event.userId}`);

    try {
      // Store event in EventStore
      await this.eventStoreService.appendToStream(
        `user-${event.userId}`,
        [event],
        -1 // Expected version (-1 means any version)
      );

      // Publish to RabbitMQ for other services
      await this.eventBusService.publish(event);

      this.logger.log(`Successfully processed UserLoggedInEvent for user: ${event.userId}`);
    } catch (error) {
      this.logger.error(`Failed to process UserLoggedInEvent for user: ${event.userId}`, error);
      throw error;
    }
  }
}
