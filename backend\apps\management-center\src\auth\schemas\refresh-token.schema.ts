import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type RefreshTokenDocument = RefreshToken & Document;

@Schema({
  timestamps: true,
  collection: 'refresh_tokens',
})
export class RefreshToken {
  @Prop({ required: true })
  token: string;

  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  userId: Types.ObjectId;

  @Prop({ required: true })
  expiresAt: Date;

  @Prop({ default: false })
  revoked: boolean;

  @Prop()
  revokedAt?: Date;

  @Prop()
  replacedByToken?: string;

  @Prop()
  userAgent?: string;

  @Prop()
  ipAddress?: string;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;

  // Method to check if token is expired
  isExpired(): boolean {
    return new Date() > this.expiresAt;
  }

  // Method to check if token is active
  isActive(): boolean {
    return !this.revoked && !this.isExpired();
  }

  // Method to revoke token
  revoke(replacedByToken?: string): void {
    this.revoked = true;
    this.revokedAt = new Date();
    if (replacedByToken) {
      this.replacedByToken = replacedByToken;
    }
  }
}

export const RefreshTokenSchema = SchemaFactory.createForClass(RefreshToken);

// Add indexes
RefreshTokenSchema.index({ token: 1 }, { unique: true });
RefreshTokenSchema.index({ userId: 1 });
RefreshTokenSchema.index({ expiresAt: 1 });
RefreshTokenSchema.index({ revoked: 1 });
RefreshTokenSchema.index({ createdAt: 1 });

// Add TTL index to automatically remove expired tokens
RefreshTokenSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });
