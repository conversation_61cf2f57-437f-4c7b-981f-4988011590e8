version: '3.8'

services:
  # ================================
  # Shell Application (Main Container)
  # ================================
  shell:
    build:
      context: ..
      dockerfile: docker/shell.Dockerfile
    container_name: app-front-shell
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    networks:
      - app-front-network
    depends_on:
      - auth
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.shell.rule=Host(`localhost`)"
      - "traefik.http.services.shell.loadbalancer.server.port=3000"

  # ================================
  # Auth Micro Frontend
  # ================================
  auth:
    build:
      context: ..
      dockerfile: docker/auth.Dockerfile
    container_name: app-front-auth
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
    networks:
      - app-front-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.auth.rule=Host(`localhost`) && PathPrefix(`/auth`)"
      - "traefik.http.services.auth.loadbalancer.server.port=3001"

  # ================================
  # Dashboard Micro Frontend (Placeholder)
  # ================================
  dashboard:
    image: nginx:alpine
    container_name: app-front-dashboard
    ports:
      - "3002:80"
    volumes:
      - ../microfrontends/dashboard/dist:/usr/share/nginx/html:ro
    environment:
      - NODE_ENV=production
    networks:
      - app-front-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`localhost`) && PathPrefix(`/dashboard`)"
      - "traefik.http.services.dashboard.loadbalancer.server.port=80"

  # ================================
  # Profile Micro Frontend (Placeholder)
  # ================================
  profile:
    image: nginx:alpine
    container_name: app-front-profile
    ports:
      - "3003:80"
    volumes:
      - ../microfrontends/profile/dist:/usr/share/nginx/html:ro
    environment:
      - NODE_ENV=production
    networks:
      - app-front-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.profile.rule=Host(`localhost`) && PathPrefix(`/profile`)"
      - "traefik.http.services.profile.loadbalancer.server.port=80"

  # ================================
  # Reverse Proxy (Optional - Traefik)
  # ================================
  traefik:
    image: traefik:v2.10
    container_name: app-front-traefik
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
    ports:
      - "80:80"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - app-front-network
    restart: unless-stopped

networks:
  app-front-network:
    driver: bridge
    name: app-front-network

volumes:
  node_modules_cache:
    driver: local
