import { Injectable, ConflictException, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CommandBus, EventBus } from '@nestjs/cqrs';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { User, UserDocument, UserStatus } from './schemas/user.schema';
import { RefreshToken, RefreshTokenDocument } from './schemas/refresh-token.schema';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { AuthResponseDto, UserResponseDto, RefreshTokenResponseDto } from './dto/auth-response.dto';
import { RegisterUserCommand } from './commands/register-user.command';
import { LoginUserCommand } from './commands/login-user.command';
import { JwtPayload } from './strategies/jwt.strategy';

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(User.name, 'write')
    private userWriteModel: Model<UserDocument>,
    @InjectModel(User.name, 'read')
    private userReadModel: Model<UserDocument>,
    @InjectModel(RefreshToken.name, 'write')
    private refreshTokenWriteModel: Model<RefreshTokenDocument>,
    @InjectModel(RefreshToken.name, 'read')
    private refreshTokenReadModel: Model<RefreshTokenDocument>,
    private jwtService: JwtService,
    private commandBus: CommandBus,
    private eventBus: EventBus,
  ) {}

  async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
    // Validate password confirmation
    if (registerDto.password !== registerDto.confirmPassword) {
      throw new BadRequestException('Passwords do not match');
    }

    // Check if user already exists
    const existingUser = await this.userReadModel.findOne({ email: registerDto.email }).exec();
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Execute register command
    const command = new RegisterUserCommand(registerDto);
    const user = await this.commandBus.execute(command);

    // Generate tokens
    const tokens = await this.generateTokens(user);

    return {
      ...tokens,
      user: this.mapUserToResponse(user),
    };
  }

  async login(loginDto: LoginDto, userAgent?: string, ipAddress?: string): Promise<AuthResponseDto> {
    const command = new LoginUserCommand(loginDto, userAgent, ipAddress);
    const { user, refreshToken } = await this.commandBus.execute(command);

    // Generate access token
    const accessToken = await this.generateAccessToken(user);

    return {
      accessToken,
      refreshToken: refreshToken.token,
      expiresIn: 24 * 60 * 60, // 24 hours in seconds
      tokenType: 'Bearer',
      user: this.mapUserToResponse(user),
    };
  }

  async validateUser(email: string, password: string): Promise<UserDocument | null> {
    const user = await this.userReadModel.findOne({ email }).exec();
    
    if (!user) {
      return null;
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return null;
    }

    if (!user.isActive()) {
      throw new UnauthorizedException('Account is not active or email not verified');
    }

    return user;
  }

  async refreshTokens(refreshToken: string): Promise<RefreshTokenResponseDto> {
    const tokenDoc = await this.refreshTokenReadModel
      .findOne({ token: refreshToken })
      .populate('userId')
      .exec();

    if (!tokenDoc || !tokenDoc.isActive()) {
      throw new UnauthorizedException('Invalid or expired refresh token');
    }

    const user = await this.userReadModel.findById(tokenDoc.userId).exec();
    if (!user || !user.isActive()) {
      throw new UnauthorizedException('User not found or inactive');
    }

    // Revoke old token
    tokenDoc.revoke();
    await tokenDoc.save();

    // Generate new tokens
    const tokens = await this.generateTokens(user);

    return tokens;
  }

  async logout(refreshToken: string): Promise<void> {
    const tokenDoc = await this.refreshTokenWriteModel.findOne({ token: refreshToken }).exec();
    
    if (tokenDoc) {
      tokenDoc.revoke();
      await tokenDoc.save();
    }
  }

  async revokeAllTokens(userId: string): Promise<void> {
    await this.refreshTokenWriteModel.updateMany(
      { userId: new Types.ObjectId(userId), revoked: false },
      { revoked: true, revokedAt: new Date() }
    ).exec();
  }

  private async generateTokens(user: UserDocument): Promise<RefreshTokenResponseDto> {
    const accessToken = await this.generateAccessToken(user);
    const refreshToken = await this.generateRefreshToken(user);

    return {
      accessToken,
      refreshToken: refreshToken.token,
      expiresIn: 24 * 60 * 60, // 24 hours in seconds
      tokenType: 'Bearer',
    };
  }

  private async generateAccessToken(user: UserDocument): Promise<string> {
    const payload: JwtPayload = {
      sub: user._id.toString(),
      email: user.email,
      role: user.role,
      organizationId: user.organizationId?.toString(),
    };

    return this.jwtService.sign(payload);
  }

  private async generateRefreshToken(user: UserDocument): Promise<RefreshTokenDocument> {
    const token = uuidv4();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    const refreshToken = new this.refreshTokenWriteModel({
      token,
      userId: user._id,
      expiresAt,
    });

    return refreshToken.save();
  }

  private mapUserToResponse(user: UserDocument): UserResponseDto {
    return {
      id: user._id.toString(),
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      fullName: user.fullName,
      phone: user.phone,
      role: user.role,
      status: user.status,
      emailVerified: user.emailVerified,
      organizationId: user.organizationId?.toString(),
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }
}
