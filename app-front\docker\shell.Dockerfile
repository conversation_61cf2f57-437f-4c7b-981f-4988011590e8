# Shell Application Dockerfile
FROM node:20-alpine AS builder

WORKDIR /app

# Install pnpm
RUN npm install -g pnpm

# Copy package files
COPY package*.json pnpm-lock.yaml* ./
COPY packages/ui-components/package.json ./packages/ui-components/
COPY packages/types/package.json ./packages/types/
COPY packages/utils/package.json ./packages/utils/
COPY shell/package.json ./shell/

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy and build shared packages
COPY packages/ ./packages/
WORKDIR /app/packages/ui-components
RUN pnpm run build || echo "Build completed"

WORKDIR /app/packages/types
RUN pnpm run build || echo "No build script for types"

WORKDIR /app/packages/utils
RUN pnpm run build || echo "No build script for utils"

# Copy and build shell
WORKDIR /app
COPY shell/ ./shell/
WORKDIR /app/shell
RUN pnpm run build

# Production stage
FROM nginx:alpine AS production

# Copy built files
COPY --from=builder /app/shell/dist /usr/share/nginx/html

# Copy nginx configuration
COPY docker/nginx/shell.conf /etc/nginx/conf.d/default.conf

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000

CMD ["nginx", "-g", "daemon off;"]
