import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CqrsModule } from '@nestjs/cqrs';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User, UserSchema } from '../auth/schemas/user.schema';
import { UserCreatedHandler } from './handlers/user-created.handler';
import { CreateUserHandler } from './commands/handlers/create-user.handler';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
    ], 'write'),
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
    ], 'read'),
    CqrsModule,
  ],
  controllers: [UsersController],
  providers: [
    UsersService,
    CreateUser<PERSON><PERSON>ler,
    UserCreatedHandler,
  ],
  exports: [UsersService],
})
export class UsersModule {}
