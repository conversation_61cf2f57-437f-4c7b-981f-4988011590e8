import { DomainEvent } from '@enterprise/common';

export class UserRegisteredEvent extends DomainEvent {
  constructor(
    public readonly userId: string,
    public readonly email: string,
    public readonly firstName: string,
    public readonly lastName: string,
    public readonly role: string,
    public readonly emailVerificationToken: string,
    public readonly organizationId?: string,
  ) {
    super('User', userId, 'UserRegistered', 1);
  }
}
