import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EventBus } from '@nestjs/cqrs';
import { Injectable, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { RegisterUserCommand } from '../register-user.command';
import { User, UserDocument, UserRole } from '../../schemas/user.schema';
import { UserRegisteredEvent } from '../../events/user-registered.event';

@CommandHandler(RegisterUserCommand)
@Injectable()
export class RegisterUserHandler implements ICommandHandler<RegisterUserCommand> {
  constructor(
    @InjectModel(User.name, 'write')
    private userModel: Model<UserDocument>,
    private eventBus: EventBus,
  ) {}

  async execute(command: RegisterUserCommand): Promise<UserDocument> {
    const { registerDto } = command;

    // Check if user already exists
    const existingUser = await this.userModel.findOne({ email: registerDto.email }).exec();
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(registerDto.password, saltRounds);

    // Generate email verification token
    const emailVerificationToken = uuidv4();
    const emailVerificationExpires = new Date();
    emailVerificationExpires.setHours(emailVerificationExpires.getHours() + 24); // 24 hours

    // Create user
    const user = new this.userModel({
      email: registerDto.email,
      password: hashedPassword,
      firstName: registerDto.firstName,
      lastName: registerDto.lastName,
      phone: registerDto.phone,
      role: registerDto.role || UserRole.USER,
      organizationId: registerDto.organizationId ? new Types.ObjectId(registerDto.organizationId) : undefined,
      emailVerificationToken,
      emailVerificationExpires,
    });

    const savedUser = await user.save();

    // Publish domain event
    const event = new UserRegisteredEvent(
      savedUser._id.toString(),
      savedUser.email,
      savedUser.firstName,
      savedUser.lastName,
      savedUser.role,
      emailVerificationToken,
      savedUser.organizationId?.toString(),
    );

    this.eventBus.publish(event);

    return savedUser;
  }
}
