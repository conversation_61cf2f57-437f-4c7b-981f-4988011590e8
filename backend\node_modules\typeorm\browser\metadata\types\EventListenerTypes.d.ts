/**
 * All types that entity listener can be.
 */
export type EventListenerType = "after-load" | "before-insert" | "after-insert" | "before-update" | "after-update" | "before-remove" | "after-remove" | "before-soft-remove" | "after-soft-remove" | "before-recover" | "after-recover";
/**
 * Provides a constants for each entity listener type.
 */
export declare class EventListenerTypes {
    static AFTER_LOAD: "after-load";
    static BEFORE_INSERT: "before-insert";
    static AFTER_INSERT: "after-insert";
    static BEFORE_UPDATE: "before-update";
    static AFTER_UPDATE: "after-update";
    static BEFORE_REMOVE: "before-remove";
    static AFTER_REMOVE: "after-remove";
    static BEFORE_SOFT_REMOVE: "before-soft-remove";
    static AFTER_SOFT_REMOVE: "after-soft-remove";
    static BEFORE_RECOVER: "before-recover";
    static AFTER_RECOVER: "after-recover";
}
