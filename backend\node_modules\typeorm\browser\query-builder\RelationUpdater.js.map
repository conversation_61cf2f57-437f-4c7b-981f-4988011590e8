{"version": 3, "sources": ["../browser/src/query-builder/RelationUpdater.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AACvC,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AAEjD;;;;GAIG;AACH,MAAM,OAAO,eAAe;IACxB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACc,YAA+B,EAC/B,aAAiC;QADjC,iBAAY,GAAZ,YAAY,CAAmB;QAC/B,kBAAa,GAAb,aAAa,CAAoB;IAC5C,CAAC;IAEJ,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,KAAkB;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAA;QAEpD,IAAI,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YACnD,MAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CACzC,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE;gBACtB,MAAM,aAAa,GAAG,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;oBAC7C,CAAC,CAAC,UAAU,CAAC,gBAAiB,CAAC,cAAc,CAAC,KAAK,CAAC;oBACpD,CAAC,CAAC,KAAK,CAAA;gBACX,UAAU,CAAC,cAAc,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA;gBACnD,OAAO,SAAS,CAAA;YACpB,CAAC,EACD,EAAS,CACZ,CAAA;YAED,IACI,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;gBACtB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;oBACjC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,MAAM,CAAC;gBAElC,OAAM;YAEV,MAAM,IAAI,CAAC,YAAY;iBAClB,kBAAkB,EAAE;iBACpB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC;iBACtC,GAAG,CAAC,SAAS,CAAC;iBACd,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;iBACjC,OAAO,EAAE,CAAA;QAClB,CAAC;aAAM,IACH,CAAC,QAAQ,CAAC,kBAAkB,IAAI,QAAQ,CAAC,WAAW,CAAC;YACrD,KAAK,KAAK,IAAI,EAChB,CAAC;YACC,qCAAqC;YAErC,MAAM,SAAS,GAAkB,EAAE,CAAA;YACnC,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBACrD,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,CAAA;YACzC,CAAC,CAAC,CAAA;YAEF,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;gBACvB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;YAC7B,MAAM,UAAU,GAAkB,EAAE,CAAA;YACpC,MAAM,UAAU,GAAa,EAAE,CAAA;YAC/B,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE;gBACxB,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAC,GAAG,CACrC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;oBACpB,MAAM,aAAa,GACf,aAAa,GAAG,OAAO,GAAG,GAAG,GAAG,WAAW,CAAA;oBAC/C,UAAU,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAChD,CAAC,CAAC,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC;wBAC7C,CAAC,CAAC,EAAE,CAAA;oBACR,UAAU,CAAC,IAAI,CACX,GAAG,MAAM,CAAC,YAAY,OAAO,aAAa,EAAE,CAC/C,CAAA;gBACL,CAAC,CACJ,CAAA;YACL,CAAC,CAAC,CAAA;YACF,MAAM,SAAS,GAAG,UAAU;iBACvB,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;iBAC7B,IAAI,CAAC,MAAM,CAAC,CAAA;YACjB,IAAI,CAAC,SAAS;gBAAE,OAAM;YAEtB,MAAM,IAAI,CAAC,YAAY;iBAClB,kBAAkB,EAAE;iBACpB,MAAM,CAAC,QAAQ,CAAC,qBAAqB,CAAC,MAAM,CAAC;iBAC7C,GAAG,CAAC,SAAS,CAAC;iBACd,KAAK,CAAC,SAAS,CAAC;iBAChB,aAAa,CAAC,UAAU,CAAC;iBACzB,OAAO,EAAE,CAAA;QAClB,CAAC;aAAM,IAAI,QAAQ,CAAC,kBAAkB,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC7D,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,YAAY,CAClB,yHAAyH,CAC5H,CAAA;YAEL,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAA;YAChC,MAAM,SAAS,GAAG,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAC,MAAM,CAC1D,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE;gBACtB,MAAM,aAAa,GAAG,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC1C,CAAC,CAAC,UAAU,CAAC,gBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC;oBACjD,CAAC,CAAC,EAAE,CAAA;gBACR,UAAU,CAAC,cAAc,CAAC,SAAS,EAAE,aAAa,CAAC,CAAA;gBACnD,OAAO,SAAS,CAAA;YACpB,CAAC,EACD,EAAS,CACZ,CAAA;YAED,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAAE,OAAM;YAE7D,MAAM,IAAI,CAAC,YAAY;iBAClB,kBAAkB,EAAE;iBACpB,MAAM,CAAC,QAAQ,CAAC,qBAAqB,CAAC,MAAM,CAAC;iBAC7C,GAAG,CAAC,SAAS,CAAC;iBACd,UAAU,CAAC,KAAK,CAAC;iBACjB,OAAO,EAAE,CAAA;QAClB,CAAC;aAAM,CAAC;YACJ,eAAe;YACf,MAAM,gBAAgB,GAAG,QAAQ,CAAC,sBAAuB,CAAA;YACzD,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;gBACvB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;YAC7B,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;YACrD,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAA;YACnE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAA;YAEpE,MAAM,YAAY,GAAoB,EAAE,CAAA;YACxC,iBAAiB,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;gBACzC,kBAAkB,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,EAAE;oBAC3C,MAAM,QAAQ,GAAkB,EAAE,CAAA;oBAClC,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;wBAC7C,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,WAAW,CAAC,QAAQ,CAChD,cAAc,CACjB;4BACG,CAAC,CAAC,MAAM,CAAC,gBAAiB,CAAC,cAAc,CACnC,cAAc,CACjB;4BACH,CAAC,CAAC,cAAc,CAAA;oBACxB,CAAC,CAAC,CAAA;oBACF,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;wBAC/C,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,WAAW,CAAC,QAAQ,CAChD,eAAe,CAClB;4BACG,CAAC,CAAC,MAAM,CAAC,gBAAiB,CAAC,cAAc,CACnC,eAAe,CAClB;4BACH,CAAC,CAAC,eAAe,CAAA;oBACzB,CAAC,CAAC,CAAA;oBACF,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAC/B,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,IAAI,CAAC,YAAY,CAAC,MAAM;gBAAE,OAAM;YAEhC,IACI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;gBAC7D,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,EAC5D,CAAC;gBACC,MAAM,OAAO,CAAC,GAAG,CACb,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvB,OAAO,IAAI,CAAC,YAAY;yBACnB,kBAAkB,EAAE;yBACpB,MAAM,EAAE;yBACR,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;yBAChC,MAAM,CAAC,KAAK,CAAC;yBACb,OAAO,EAAE,CAAA;gBAClB,CAAC,CAAC,CACL,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,CAAC,YAAY;qBAClB,kBAAkB,EAAE;qBACpB,MAAM,EAAE;qBACR,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;qBAChC,MAAM,CAAC,YAAY,CAAC;qBACpB,OAAO,EAAE,CAAA;YAClB,CAAC;QACL,CAAC;IACL,CAAC;CACJ", "file": "RelationUpdater.js", "sourcesContent": ["import { QueryBuilder } from \"./QueryBuilder\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { QueryExpressionMap } from \"./QueryExpressionMap\"\nimport { TypeORMError } from \"../error\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\n\n/**\n * Allows to work with entity relations and perform specific operations with those relations.\n *\n * todo: add transactions everywhere\n */\nexport class RelationUpdater {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        protected queryBuilder: QueryBuilder<any>,\n        protected expressionMap: QueryExpressionMap,\n    ) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs set or add operation on a relation.\n     */\n    async update(value: any | any[]): Promise<void> {\n        const relation = this.expressionMap.relationMetadata\n\n        if (relation.isManyToOne || relation.isOneToOneOwner) {\n            const updateSet = relation.joinColumns.reduce(\n                (updateSet, joinColumn) => {\n                    const relationValue = ObjectUtils.isObject(value)\n                        ? joinColumn.referencedColumn!.getEntityValue(value)\n                        : value\n                    joinColumn.setEntityValue(updateSet, relationValue)\n                    return updateSet\n                },\n                {} as any,\n            )\n\n            if (\n                !this.expressionMap.of ||\n                (Array.isArray(this.expressionMap.of) &&\n                    !this.expressionMap.of.length)\n            )\n                return\n\n            await this.queryBuilder\n                .createQueryBuilder()\n                .update(relation.entityMetadata.target)\n                .set(updateSet)\n                .whereInIds(this.expressionMap.of)\n                .execute()\n        } else if (\n            (relation.isOneToOneNotOwner || relation.isOneToMany) &&\n            value === null\n        ) {\n            // we handle null a bit different way\n\n            const updateSet: ObjectLiteral = {}\n            relation.inverseRelation!.joinColumns.forEach((column) => {\n                updateSet[column.propertyName] = null\n            })\n\n            const ofs = Array.isArray(this.expressionMap.of)\n                ? this.expressionMap.of\n                : [this.expressionMap.of]\n            const parameters: ObjectLiteral = {}\n            const conditions: string[] = []\n            ofs.forEach((of, ofIndex) => {\n                relation.inverseRelation!.joinColumns.map(\n                    (column, columnIndex) => {\n                        const parameterName =\n                            \"joinColumn_\" + ofIndex + \"_\" + columnIndex\n                        parameters[parameterName] = ObjectUtils.isObject(of)\n                            ? column.referencedColumn!.getEntityValue(of)\n                            : of\n                        conditions.push(\n                            `${column.propertyPath} = :${parameterName}`,\n                        )\n                    },\n                )\n            })\n            const condition = conditions\n                .map((str) => \"(\" + str + \")\")\n                .join(\" OR \")\n            if (!condition) return\n\n            await this.queryBuilder\n                .createQueryBuilder()\n                .update(relation.inverseEntityMetadata.target)\n                .set(updateSet)\n                .where(condition)\n                .setParameters(parameters)\n                .execute()\n        } else if (relation.isOneToOneNotOwner || relation.isOneToMany) {\n            if (Array.isArray(this.expressionMap.of))\n                throw new TypeORMError(\n                    `You cannot update relations of multiple entities with the same related object. Provide a single entity into .of method.`,\n                )\n\n            const of = this.expressionMap.of\n            const updateSet = relation.inverseRelation!.joinColumns.reduce(\n                (updateSet, joinColumn) => {\n                    const relationValue = ObjectUtils.isObject(of)\n                        ? joinColumn.referencedColumn!.getEntityValue(of)\n                        : of\n                    joinColumn.setEntityValue(updateSet, relationValue)\n                    return updateSet\n                },\n                {} as any,\n            )\n\n            if (!value || (Array.isArray(value) && !value.length)) return\n\n            await this.queryBuilder\n                .createQueryBuilder()\n                .update(relation.inverseEntityMetadata.target)\n                .set(updateSet)\n                .whereInIds(value)\n                .execute()\n        } else {\n            // many to many\n            const junctionMetadata = relation.junctionEntityMetadata!\n            const ofs = Array.isArray(this.expressionMap.of)\n                ? this.expressionMap.of\n                : [this.expressionMap.of]\n            const values = Array.isArray(value) ? value : [value]\n            const firstColumnValues = relation.isManyToManyOwner ? ofs : values\n            const secondColumnValues = relation.isManyToManyOwner ? values : ofs\n\n            const bulkInserted: ObjectLiteral[] = []\n            firstColumnValues.forEach((firstColumnVal) => {\n                secondColumnValues.forEach((secondColumnVal) => {\n                    const inserted: ObjectLiteral = {}\n                    junctionMetadata.ownerColumns.forEach((column) => {\n                        inserted[column.databaseName] = ObjectUtils.isObject(\n                            firstColumnVal,\n                        )\n                            ? column.referencedColumn!.getEntityValue(\n                                  firstColumnVal,\n                              )\n                            : firstColumnVal\n                    })\n                    junctionMetadata.inverseColumns.forEach((column) => {\n                        inserted[column.databaseName] = ObjectUtils.isObject(\n                            secondColumnVal,\n                        )\n                            ? column.referencedColumn!.getEntityValue(\n                                  secondColumnVal,\n                              )\n                            : secondColumnVal\n                    })\n                    bulkInserted.push(inserted)\n                })\n            })\n\n            if (!bulkInserted.length) return\n\n            if (\n                this.queryBuilder.connection.driver.options.type === \"oracle\" ||\n                this.queryBuilder.connection.driver.options.type === \"sap\"\n            ) {\n                await Promise.all(\n                    bulkInserted.map((value) => {\n                        return this.queryBuilder\n                            .createQueryBuilder()\n                            .insert()\n                            .into(junctionMetadata.tableName)\n                            .values(value)\n                            .execute()\n                    }),\n                )\n            } else {\n                await this.queryBuilder\n                    .createQueryBuilder()\n                    .insert()\n                    .into(junctionMetadata.tableName)\n                    .values(bulkInserted)\n                    .execute()\n            }\n        }\n    }\n}\n"], "sourceRoot": ".."}