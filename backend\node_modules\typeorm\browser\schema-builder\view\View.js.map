{"version": 3, "sources": ["../browser/src/schema-builder/view/View.ts"], "names": [], "mappings": "AASA;;GAEG;AACH,MAAM,OAAO,IAAI;IAqCb,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,OAAqB;QAxCxB,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAyCvC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;QACjB,IAAI,OAAO,EAAE,CAAC;YACV,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;YAChC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;YAC5B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;YACxB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAA;YACpC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,OAAO,CAAC,YAAY,CAAA;QAC9C,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK;QACD,OAAO,IAAI,IAAI,CAAc;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,YAAY,EAAE,IAAI,CAAC,YAAY;SAClC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,KAAiB;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,SAAqB;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAC3B,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAA;QACvD,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,cAA8B,EAAE,MAAc;QACxD,MAAM,OAAO,GAAgB;YACzB,QAAQ,EAAE,cAAc,CAAC,QAAQ;YACjC,MAAM,EAAE,cAAc,CAAC,MAAM;YAC7B,IAAI,EAAE,MAAM,CAAC,cAAc,CACvB,cAAc,CAAC,SAAS,EACxB,cAAc,CAAC,MAAM,EACrB,cAAc,CAAC,QAAQ,CAC1B;YACD,UAAU,EAAE,cAAc,CAAC,UAAW;YACtC,YAAY,EAAE,cAAc,CAAC,iBAAiB,CAAC,YAAY;SAC9D,CAAA;QAED,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,CAAA;IAC5B,CAAC;CACJ", "file": "View.js", "sourcesContent": ["import {\n    DataSource,\n    Driver,\n    EntityMetadata,\n    SelectQueryBuilder,\n    TableIndex,\n} from \"../..\"\nimport { ViewOptions } from \"../options/ViewOptions\"\n\n/**\n * View in the database represented in this class.\n */\nexport class View {\n    readonly \"@instanceof\" = Symbol.for(\"View\")\n\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database name that this view resides in if it applies.\n     */\n    database?: string\n\n    /**\n     * Schema name that this view resides in if it applies.\n     */\n    schema?: string\n\n    /**\n     * View name\n     */\n    name: string\n\n    /**\n     * Indicates if view is materialized.\n     */\n    materialized: boolean\n\n    /**\n     * View Indices\n     */\n    indices: TableIndex[]\n\n    /**\n     * View definition.\n     */\n    expression: string | ((connection: DataSource) => SelectQueryBuilder<any>)\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(options?: ViewOptions) {\n        this.indices = []\n        if (options) {\n            this.database = options.database\n            this.schema = options.schema\n            this.name = options.name\n            this.expression = options.expression\n            this.materialized = !!options.materialized\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Clones this table to a new table with all properties cloned.\n     */\n    clone(): View {\n        return new View(<ViewOptions>{\n            database: this.database,\n            schema: this.schema,\n            name: this.name,\n            expression: this.expression,\n            materialized: this.materialized,\n        })\n    }\n\n    /**\n     * Add index\n     */\n    addIndex(index: TableIndex): void {\n        this.indices.push(index)\n    }\n\n    /**\n     * Remove index\n     */\n    removeIndex(viewIndex: TableIndex): void {\n        const index = this.indices.find(\n            (index) => index.name === viewIndex.name,\n        )\n        if (index) {\n            this.indices.splice(this.indices.indexOf(index), 1)\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates view from a given entity metadata.\n     */\n    static create(entityMetadata: EntityMetadata, driver: Driver): View {\n        const options: ViewOptions = {\n            database: entityMetadata.database,\n            schema: entityMetadata.schema,\n            name: driver.buildTableName(\n                entityMetadata.tableName,\n                entityMetadata.schema,\n                entityMetadata.database,\n            ),\n            expression: entityMetadata.expression!,\n            materialized: entityMetadata.tableMetadataArgs.materialized,\n        }\n\n        return new View(options)\n    }\n}\n"], "sourceRoot": "../.."}