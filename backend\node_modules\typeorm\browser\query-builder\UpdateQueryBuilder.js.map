{"version": 3, "sources": ["../browser/src/query-builder/UpdateQueryBuilder.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAM7C,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAA;AACpD,OAAO,EAAE,mCAAmC,EAAE,MAAM,8CAA8C,CAAA;AAClG,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAA;AAG/E,OAAO,EAAE,8BAA8B,EAAE,MAAM,yCAAyC,CAAA;AACxF,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAA;AAG5E,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AACvC,OAAO,EAAE,2BAA2B,EAAE,MAAM,sCAAsC,CAAA;AAElF,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAA;AAEnD;;GAEG;AACH,MAAM,OAAO,kBACT,SAAQ,YAAoB;IAK5B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACI,wBAAwD,EACxD,WAAyB;QAEzB,KAAK,CAAC,wBAA+B,EAAE,WAAW,CAAC,CAAA;QAV9C,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;QAWrD,IAAI,CAAC,aAAa,CAAC,yBAAyB,GAAG,KAAK,CAAA;IACxD,CAAC;IAED,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAE5E;;OAEG;IACH,QAAQ;QACJ,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAC9B,GAAG,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAA;QACjC,GAAG,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAA;QACpC,GAAG,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACrC,GAAG,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACnC,OAAO,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,sBAAsB,GAAY,KAAK,CAAA;QAE3C,IAAI,CAAC;YACD,sCAAsC;YACtC,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,IAAI;gBAC1C,WAAW,CAAC,mBAAmB,KAAK,KAAK,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;gBACpC,sBAAsB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,4DAA4D;YAC5D,IACI,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,IAAI;gBACzC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,CACnC,cAAc,EACd,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,EACtC,IAAI,CAAC,aAAa,CAAC,SAAS,CAC/B,CAAA;YACL,CAAC;YAED,IAAI,UAAU,GAAkB,IAAI,CAAA;YACpC,IAAI,eAAe,GAAkB,IAAI,CAAA;YAEzC,yFAAyF;YACzF,MAAM,6BAA6B,GAC/B,IAAI,6BAA6B,CAC7B,WAAW,EACX,IAAI,CAAC,aAAa,CACrB,CAAA;YAEL,MAAM,gBAAgB,GAAqB,EAAE,CAAA;YAE7C,IACI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;gBAC3C,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;oBACpD,gBAAgB,CAAC,IAAI,CACjB,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,2BAA2B,CACjE,UAAU,CACb,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IACI,IAAI,CAAC,aAAa,CAAC,YAAY,KAAK,IAAI;gBACxC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW;gBACzC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAC7C,CAAC;gBACC,IAAI,CAAC,aAAa,CAAC,qBAAqB;oBACpC,6BAA6B,CAAC,2BAA2B,EAAE,CAAA;gBAE/D,gBAAgB,CAAC,IAAI,CACjB,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,MAAM,CAC9C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CACvC,CACJ,CAAA;YACL,CAAC;YAED,IACI,gBAAgB,CAAC,MAAM,GAAG,CAAC;gBAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EACjD,CAAC;gBACC,UAAU,GACN,IAAI,CAAC,UAAU,CAAC,MACnB,CAAC,6BAA6B,CAC3B,cAAc,EACd,gBAAgB,CACnB,CAAA;gBACD,eAAe,GAAG,4BAA4B,CAAA;YAClD,CAAC;YAED,uBAAuB;YACvB,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;YAE5D,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,eAAe,CAAC,CAAA;YAC3D,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,KAAK,CACvC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EACrD,UAAU,EACV,IAAI,CACP,CAAA;YACD,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAEnD,qIAAqI;YACrI,IACI,IAAI,CAAC,aAAa,CAAC,YAAY,KAAK,IAAI;gBACxC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW;gBACzC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAC7C,CAAC;gBACC,MAAM,6BAA6B,CAAC,MAAM,CACtC,YAAY,EACZ,IAAI,CAAC,aAAa,CAAC,aAAa,CACnC,CAAA;YACL,CAAC;YAED,2DAA2D;YAC3D,IACI,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,IAAI;gBACzC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,CACnC,aAAa,EACb,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,EACtC,IAAI,CAAC,aAAa,CAAC,SAAS,CAC/B,CAAA;YACL,CAAC;YAED,qCAAqC;YACrC,IAAI,sBAAsB;gBAAE,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;YAEjE,OAAO,YAAY,CAAA;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACP,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnC,wCAAwC;gBACxC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;YAC/B,CAAC;QACL,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,GAAG,CAAC,MAAsC;QACtC,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,MAAM,CAAA;QACrC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;;OAKG;IACH,KAAK,CACD,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,CAAA,CAAC,oFAAoF;QACnH,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC/C,IAAI,SAAS;YACT,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;gBACxB,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE;aAC3C,CAAA;QACL,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,QAAQ,CACJ,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;YAC3B,IAAI,EAAE,KAAK;YACX,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;SAC3C,CAAC,CAAA;QACF,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,OAAO,CACH,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;YAC3B,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;SAC3C,CAAC,CAAA;QACF,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,GAAgB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IACvD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,GAAgB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IAC1D,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,GAAgB;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IACzD,CAAC;IAkBD;;OAEG;IACH,MAAM,CAAC,MAAyB;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;IACjC,CAAC;IAmBD;;OAEG;IACH,SAAS,CAAC,SAA4B;QAClC,mDAAmD;QACnD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,mCAAmC,EAAE,CAAA;QACnD,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS,CAAA;QACxC,OAAO,IAAI,CAAA;IACf,CAAC;IA6BD;;;;OAIG;IACH,OAAO,CACH,IAAgC,EAChC,QAAwB,KAAK,EAC7B,KAAoC;QAEpC,IAAI,IAAI,EAAE,CAAC;YACP,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC3B,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAwB,CAAA;YAC1D,CAAC;iBAAM,CAAC;gBACJ,IAAI,KAAK,EAAE,CAAC;oBACR,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG;wBAC1B,CAAC,IAAc,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;qBACrC,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,CAAC,IAAc,CAAC,EAAE,KAAK,EAAE,CAAA;gBAC7D,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,EAAE,CAAA;QACpC,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,UAAU,CACN,IAAY,EACZ,QAAwB,KAAK,EAC7B,KAAoC;QAEpC,IAAI,KAAK,EAAE,CAAC;YACR,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAA;QACxD,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAA;QAC7C,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAc;QAChB,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,KAAK,CAAA;QAChC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,WAAW,CAAC,MAAyB;QACjC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW;YAC1C,MAAM,IAAI,YAAY,CAClB,iFAAiF,CACpF,CAAA;QAEL,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,CAAA;QAC9B,MAAM,QAAQ,GAAa,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;QACpE,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACxB,MAAM,WAAW,GACb,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YACjE,IAAI,CAAC,WAAW;gBACZ,MAAM,IAAI,YAAY,CAClB,kEAAkE,CACrE,CAAA;YAEL,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;QAClC,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG,QAAQ,CAAA;QAC3C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,YAAY,CAAC,OAAgB;QACzB,IAAI,CAAC,aAAa,CAAC,YAAY,GAAG,OAAO,CAAA;QACzC,OAAO,IAAI,CAAA;IACf,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,sBAAsB;QAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW;YACtD,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ;YACxC,CAAC,CAAC,SAAS,CAAA;QAEf,0EAA0E;QAC1E,MAAM,mBAAmB,GAAkB,EAAE,CAAA;QAC7C,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;YAC1B,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;gBAC/B,mBAAmB,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAA;YAC7C,CAAC;QACL,CAAC;QAED,2CAA2C;QAC3C,MAAM,qBAAqB,GAAa,EAAE,CAAA;QAC1C,MAAM,cAAc,GAAqB,EAAE,CAAA;QAC3C,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,mBAAmB,CAAC,CAAC,OAAO,CAC1D,CAAC,YAAY,EAAE,EAAE;gBACb,6FAA6F;gBAC7F,MAAM,OAAO,GACT,QAAQ,CAAC,2BAA2B,CAAC,YAAY,CAAC,CAAA;gBAEtD,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;oBACtB,MAAM,IAAI,2BAA2B,CACjC,YAAY,EACZ,QAAQ,CACX,CAAA;gBACL,CAAC;gBAED,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACvB,IACI,CAAC,MAAM,CAAC,QAAQ;wBAChB,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EACjC,CAAC;wBACC,OAAM;oBACV,CAAC;oBAED,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;oBAE3B,EAAE;oBACF,IAAI,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAA;oBACtD,IACI,MAAM,CAAC,gBAAgB;wBACvB,OAAO,KAAK,KAAK,QAAQ;wBACzB,CAAC,CAAC,KAAK,YAAY,IAAI,CAAC;wBACxB,KAAK,KAAK,IAAI;wBACd,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EACzB,CAAC;wBACC,KAAK;4BACD,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;oBACrD,CAAC;yBAAM,IAAI,CAAC,CAAC,OAAO,KAAK,KAAK,UAAU,CAAC,EAAE,CAAC;wBACxC,KAAK;4BACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,sBAAsB,CACzC,KAAK,EACL,MAAM,CACT,CAAA;oBACT,CAAC;oBAED,yBAAyB;oBACzB,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;wBAC9B,8CAA8C;wBAC9C,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;4BAC5B,KAAK;4BACL,KAAK,EAAE,CACd,CAAA;oBACL,CAAC;yBAAM,IACH,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK;wBAC1C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;4BAC/B,SAAS,CAAC;wBAClB,KAAK,KAAK,IAAI,EAChB,CAAC;wBACC,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,SAAS,CAC/C,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EACjD,CAAC;4BACC,KAAK,GACD,IAAI,CAAC,UAAU,CAAC,MACnB,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;wBACrC,CAAC;wBAED,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;wBAE7C,IAAI,UAAU,GAAG,IAAI,CAAA;wBACrB,IACI,CAAC,WAAW,CAAC,aAAa,CACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB;4BACG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;gCAC/B,cAAc,CAAC;4BACvB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CACvC,MAAM,CAAC,IAAI,CACd,KAAK,CAAC,CAAC,EACV,CAAC;4BACC,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,MAGnB,CAAC,OAAO,CAAC,oBAAoB,CAAA;4BAC9B,MAAM,YAAY,GAAG,SAAS;gCAC1B,CAAC,CAAC,cAAc;gCAChB,CAAC,CAAC,iBAAiB,CAAA;4BACvB,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;gCACtB,UAAU,GAAG,GAAG,YAAY,IAAI,SAAS,KAAK,MAAM,CAAC,IAAI,GAAG,CAAA;4BAChE,CAAC;iCAAM,CAAC;gCACJ,UAAU,GAAG,GAAG,YAAY,IAAI,SAAS,GAAG,CAAA;4BAChD,CAAC;wBACL,CAAC;6BAAM,IACH,WAAW,CAAC,gBAAgB,CACxB,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB;4BACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CACvC,MAAM,CAAC,IAAI,CACd,KAAK,CAAC,CAAC,EACV,CAAC;4BACC,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;gCACtB,UAAU,GAAG,iCAAiC,SAAS,MAAM,MAAM,CAAC,IAAI,MAAM,MAAM,CAAC,IAAI,EAAE,CAAA;4BAC/F,CAAC;iCAAM,CAAC;gCACJ,UAAU,GAAG,sBAAsB,SAAS,MAAM,MAAM,CAAC,IAAI,EAAE,CAAA;4BACnE,CAAC;wBACL,CAAC;6BAAM,IACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;4BAC/B,OAAO;4BACX,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CACvC,MAAM,CAAC,IAAI,CACd,KAAK,CAAC,CAAC,EACV,CAAC;4BACC,UAAU;gCACN,MAAM,CAAC,IAAI;oCACX,mBAAmB;oCACnB,SAAS;oCACT,IAAI;oCACJ,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC;oCACpB,GAAG,CAAA;wBACX,CAAC;6BAAM,CAAC;4BACJ,UAAU,GAAG,SAAS,CAAA;wBAC1B,CAAC;wBACD,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;4BAC5B,KAAK;4BACL,UAAU,CACjB,CAAA;oBACL,CAAC;gBACL,CAAC,CAAC,CAAA;YACN,CAAC,CACJ,CAAA;YAED,wEAAwE;YACxE,IACI,qBAAqB,CAAC,MAAM,GAAG,CAAC;gBAChC,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,KAAK,CAAC,EAC/C,CAAC;gBACC,IACI,QAAQ,CAAC,aAAa;oBACtB,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;oBAErD,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC;wBAC5C,KAAK;wBACL,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,YAAY,CAAC;wBAChD,MAAM,CACb,CAAA;gBACL,IACI,QAAQ,CAAC,gBAAgB;oBACzB,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;oBAExD,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CAAC;wBAC/C,sBAAsB,CAC7B,CAAA,CAAC,gFAAgF;YAC1F,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;gBACzC,MAAM,KAAK,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAA;gBAEtC,yBAAyB;gBACzB,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;oBAC9B,8CAA8C;oBAC9C,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,KAAK,EAAE,CACrC,CAAA;gBACL,CAAC;qBAAM,IACH,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK;oBAC1C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC;oBACtD,KAAK,KAAK,IAAI,EAChB,CAAC;oBACC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAA;gBAC5D,CAAC;qBAAM,CAAC;oBACJ,0GAA0G;oBAC1G,8BAA8B;oBAC9B,yCAAyC;oBAEzC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;oBAC7C,qBAAqB,CAAC,IAAI,CACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,SAAS,CACvC,CAAA;gBACL,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,qBAAqB,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,wBAAwB,EAAE,CAAA;QACxC,CAAC;QAED,iDAAiD;QACjD,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAA;QAEpE,IAAI,mBAAmB,KAAK,EAAE,EAAE,CAAC;YAC7B,OAAO,UAAU,IAAI,CAAC,YAAY,CAC9B,IAAI,CAAC,gBAAgB,EAAE,CAC1B,QAAQ,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,eAAe,EAAE,CAAA,CAAC,uDAAuD;QACzH,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClD,OAAO,UAAU,IAAI,CAAC,YAAY,CAC9B,IAAI,CAAC,gBAAgB,EAAE,CAC1B,QAAQ,qBAAqB,CAAC,IAAI,CAC/B,IAAI,CACP,WAAW,mBAAmB,GAAG,eAAe,EAAE,CAAA;QACvD,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACpD,OAAO,UAAU,IAAI,CAAC,YAAY,CAC9B,IAAI,CAAC,gBAAgB,EAAE,CAC1B,QAAQ,qBAAqB,CAAC,IAAI,CAC/B,IAAI,CACP,GAAG,eAAe,gBAAgB,mBAAmB,EAAE,CAAA;QAC5D,CAAC;QAED,OAAO,UAAU,IAAI,CAAC,YAAY,CAC9B,IAAI,CAAC,gBAAgB,EAAE,CAC1B,QAAQ,qBAAqB,CAAC,IAAI,CAC/B,IAAI,CACP,GAAG,eAAe,cAAc,mBAAmB,EAAE,CAAA;IAC1D,CAAC;IAED;;OAEG;IACO,uBAAuB;QAC7B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA;QAC5C,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC;YAChC,OAAO,CACH,YAAY;gBACZ,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;qBAChB,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;oBAChB,IAAI,OAAO,QAAQ,CAAC,UAAU,CAAC,KAAK,QAAQ,EAAE,CAAC;wBAC3C,OAAO,CACH,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;4BACrC,GAAG;4BACH,QAAQ,CAAC,UAAU,CAAC,CACvB,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,OAAO,CACH,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;4BACrC,GAAG;4BACF,QAAQ,CAAC,UAAU,CAAS,CAAC,KAAK;4BACnC,GAAG;4BACF,QAAQ,CAAC,UAAU,CAAS,CAAC,KAAK,CACtC,CAAA;oBACL,CAAC;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,CAAC,CAClB,CAAA;QAEL,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;OAEG;IACO,qBAAqB;QAC3B,MAAM,KAAK,GAAuB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAA;QAE1D,IAAI,KAAK,EAAE,CAAC;YACR,IACI,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc,EACxD,CAAC;gBACC,OAAO,SAAS,GAAG,KAAK,CAAA;YAC5B,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,8BAA8B,EAAE,CAAA;YAC9C,CAAC;QACL,CAAC;QAED,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;OAEG;IACO,WAAW;QACjB,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,QAAQ;YAChD,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAA;QAEvC,MAAM,IAAI,wBAAwB,EAAE,CAAA;IACxC,CAAC;CACJ", "file": "UpdateQueryBuilder.js", "sourcesContent": ["import { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { QueryBuilder } from \"./QueryBuilder\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { WhereExpressionBuilder } from \"./WhereExpressionBuilder\"\nimport { Brackets } from \"./Brackets\"\nimport { UpdateResult } from \"./result/UpdateResult\"\nimport { ReturningStatementNotSupportedError } from \"../error/ReturningStatementNotSupportedError\"\nimport { ReturningResultsEntityUpdator } from \"./ReturningResultsEntityUpdator\"\nimport { MysqlDriver } from \"../driver/mysql/MysqlDriver\"\nimport { OrderByCondition } from \"../find-options/OrderByCondition\"\nimport { LimitOnUpdateNotSupportedError } from \"../error/LimitOnUpdateNotSupportedError\"\nimport { UpdateValuesMissingError } from \"../error/UpdateValuesMissingError\"\nimport { QueryDeepPartialEntity } from \"./QueryPartialEntity\"\nimport { AuroraMysqlDriver } from \"../driver/aurora-mysql/AuroraMysqlDriver\"\nimport { TypeORMError } from \"../error\"\nimport { EntityPropertyNotFoundError } from \"../error/EntityPropertyNotFoundError\"\nimport { SqlServerDriver } from \"../driver/sqlserver/SqlServerDriver\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\n\n/**\n * Allows to build complex sql queries in a fashion way and execute those queries.\n */\nexport class UpdateQueryBuilder<Entity extends ObjectLiteral>\n    extends QueryBuilder<Entity>\n    implements WhereExpressionBuilder\n{\n    readonly \"@instanceof\" = Symbol.for(\"UpdateQueryBuilder\")\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        connectionOrQueryBuilder: DataSource | QueryBuilder<any>,\n        queryRunner?: QueryRunner,\n    ) {\n        super(connectionOrQueryBuilder as any, queryRunner)\n        this.expressionMap.aliasNamePrefixingEnabled = false\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets generated SQL query without parameters being replaced.\n     */\n    getQuery(): string {\n        let sql = this.createComment()\n        sql += this.createCteExpression()\n        sql += this.createUpdateExpression()\n        sql += this.createOrderByExpression()\n        sql += this.createLimitExpression()\n        return this.replacePropertyNamesForTheWholeQuery(sql.trim())\n    }\n\n    /**\n     * Executes sql generated by query builder and returns raw database results.\n     */\n    async execute(): Promise<UpdateResult> {\n        const queryRunner = this.obtainQueryRunner()\n        let transactionStartedByUs: boolean = false\n\n        try {\n            // start transaction if it was enabled\n            if (\n                this.expressionMap.useTransaction === true &&\n                queryRunner.isTransactionActive === false\n            ) {\n                await queryRunner.startTransaction()\n                transactionStartedByUs = true\n            }\n\n            // call before updation methods in listeners and subscribers\n            if (\n                this.expressionMap.callListeners === true &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                await queryRunner.broadcaster.broadcast(\n                    \"BeforeUpdate\",\n                    this.expressionMap.mainAlias!.metadata,\n                    this.expressionMap.valuesSet,\n                )\n            }\n\n            let declareSql: string | null = null\n            let selectOutputSql: string | null = null\n\n            // if update entity mode is enabled we may need extra columns for the returning statement\n            const returningResultsEntityUpdator =\n                new ReturningResultsEntityUpdator(\n                    queryRunner,\n                    this.expressionMap,\n                )\n\n            const returningColumns: ColumnMetadata[] = []\n\n            if (\n                Array.isArray(this.expressionMap.returning) &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                for (const columnPath of this.expressionMap.returning) {\n                    returningColumns.push(\n                        ...this.expressionMap.mainAlias!.metadata.findColumnsWithPropertyPath(\n                            columnPath,\n                        ),\n                    )\n                }\n            }\n\n            if (\n                this.expressionMap.updateEntity === true &&\n                this.expressionMap.mainAlias!.hasMetadata &&\n                this.expressionMap.whereEntities.length > 0\n            ) {\n                this.expressionMap.extraReturningColumns =\n                    returningResultsEntityUpdator.getUpdationReturningColumns()\n\n                returningColumns.push(\n                    ...this.expressionMap.extraReturningColumns.filter(\n                        (c) => !returningColumns.includes(c),\n                    ),\n                )\n            }\n\n            if (\n                returningColumns.length > 0 &&\n                this.connection.driver.options.type === \"mssql\"\n            ) {\n                declareSql = (\n                    this.connection.driver as SqlServerDriver\n                ).buildTableVariableDeclaration(\n                    \"@OutputTable\",\n                    returningColumns,\n                )\n                selectOutputSql = `SELECT * FROM @OutputTable`\n            }\n\n            // execute update query\n            const [updateSql, parameters] = this.getQueryAndParameters()\n\n            const statements = [declareSql, updateSql, selectOutputSql]\n            const queryResult = await queryRunner.query(\n                statements.filter((sql) => sql != null).join(\";\\n\\n\"),\n                parameters,\n                true,\n            )\n            const updateResult = UpdateResult.from(queryResult)\n\n            // if we are updating entities and entity updation is enabled we must update some of entity columns (like version, update date, etc.)\n            if (\n                this.expressionMap.updateEntity === true &&\n                this.expressionMap.mainAlias!.hasMetadata &&\n                this.expressionMap.whereEntities.length > 0\n            ) {\n                await returningResultsEntityUpdator.update(\n                    updateResult,\n                    this.expressionMap.whereEntities,\n                )\n            }\n\n            // call after updation methods in listeners and subscribers\n            if (\n                this.expressionMap.callListeners === true &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                await queryRunner.broadcaster.broadcast(\n                    \"AfterUpdate\",\n                    this.expressionMap.mainAlias!.metadata,\n                    this.expressionMap.valuesSet,\n                )\n            }\n\n            // close transaction if we started it\n            if (transactionStartedByUs) await queryRunner.commitTransaction()\n\n            return updateResult\n        } catch (error) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n            throw error\n        } finally {\n            if (queryRunner !== this.queryRunner) {\n                // means we created our own query runner\n                await queryRunner.release()\n            }\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Values needs to be updated.\n     */\n    set(values: QueryDeepPartialEntity<Entity>): this {\n        this.expressionMap.valuesSet = values\n        return this\n    }\n\n    /**\n     * Sets WHERE condition in the query builder.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     * Additionally you can add parameters used in where expression.\n     */\n    where(\n        where:\n            | string\n            | ((qb: this) => string)\n            | Brackets\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres = [] // don't move this block below since computeWhereParameter can add where expressions\n        const condition = this.getWhereCondition(where)\n        if (condition)\n            this.expressionMap.wheres = [\n                { type: \"simple\", condition: condition },\n            ]\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Adds new AND WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    andWhere(\n        where:\n            | string\n            | ((qb: this) => string)\n            | Brackets\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres.push({\n            type: \"and\",\n            condition: this.getWhereCondition(where),\n        })\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Adds new OR WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    orWhere(\n        where:\n            | string\n            | ((qb: this) => string)\n            | Brackets\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres.push({\n            type: \"or\",\n            condition: this.getWhereCondition(where),\n        })\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Sets WHERE condition in the query builder with a condition for the given ids.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     */\n    whereInIds(ids: any | any[]): this {\n        return this.where(this.getWhereInIdsCondition(ids))\n    }\n\n    /**\n     * Adds new AND WHERE with conditions for the given ids.\n     */\n    andWhereInIds(ids: any | any[]): this {\n        return this.andWhere(this.getWhereInIdsCondition(ids))\n    }\n\n    /**\n     * Adds new OR WHERE with conditions for the given ids.\n     */\n    orWhereInIds(ids: any | any[]): this {\n        return this.orWhere(this.getWhereInIdsCondition(ids))\n    }\n    /**\n     * Optional returning/output clause.\n     * This will return given column values.\n     */\n    output(columns: string[]): this\n\n    /**\n     * Optional returning/output clause.\n     * Returning is a SQL string containing returning statement.\n     */\n    output(output: string): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    output(output: string | string[]): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    output(output: string | string[]): this {\n        return this.returning(output)\n    }\n\n    /**\n     * Optional returning/output clause.\n     * This will return given column values.\n     */\n    returning(columns: string[]): this\n\n    /**\n     * Optional returning/output clause.\n     * Returning is a SQL string containing returning statement.\n     */\n    returning(returning: string): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    returning(returning: string | string[]): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    returning(returning: string | string[]): this {\n        // not all databases support returning/output cause\n        if (!this.connection.driver.isReturningSqlSupported(\"update\")) {\n            throw new ReturningStatementNotSupportedError()\n        }\n\n        this.expressionMap.returning = returning\n        return this\n    }\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     *\n     * Calling order by without order set will remove all previously set order bys.\n     */\n    orderBy(): this\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     */\n    orderBy(\n        sort: string,\n        order?: \"ASC\" | \"DESC\",\n        nulls?: \"NULLS FIRST\" | \"NULLS LAST\",\n    ): this\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     */\n    orderBy(order: OrderByCondition): this\n\n    /**\n     * Sets ORDER BY condition in the query builder.\n     * If you had previously ORDER BY expression defined,\n     * calling this function will override previously set ORDER BY conditions.\n     */\n    orderBy(\n        sort?: string | OrderByCondition,\n        order: \"ASC\" | \"DESC\" = \"ASC\",\n        nulls?: \"NULLS FIRST\" | \"NULLS LAST\",\n    ): this {\n        if (sort) {\n            if (typeof sort === \"object\") {\n                this.expressionMap.orderBys = sort as OrderByCondition\n            } else {\n                if (nulls) {\n                    this.expressionMap.orderBys = {\n                        [sort as string]: { order, nulls },\n                    }\n                } else {\n                    this.expressionMap.orderBys = { [sort as string]: order }\n                }\n            }\n        } else {\n            this.expressionMap.orderBys = {}\n        }\n        return this\n    }\n\n    /**\n     * Adds ORDER BY condition in the query builder.\n     */\n    addOrderBy(\n        sort: string,\n        order: \"ASC\" | \"DESC\" = \"ASC\",\n        nulls?: \"NULLS FIRST\" | \"NULLS LAST\",\n    ): this {\n        if (nulls) {\n            this.expressionMap.orderBys[sort] = { order, nulls }\n        } else {\n            this.expressionMap.orderBys[sort] = order\n        }\n        return this\n    }\n\n    /**\n     * Sets LIMIT - maximum number of rows to be selected.\n     */\n    limit(limit?: number): this {\n        this.expressionMap.limit = limit\n        return this\n    }\n\n    /**\n     * Indicates if entity must be updated after update operation.\n     * This may produce extra query or use RETURNING / OUTPUT statement (depend on database).\n     * Enabled by default.\n     */\n    whereEntity(entity: Entity | Entity[]): this {\n        if (!this.expressionMap.mainAlias!.hasMetadata)\n            throw new TypeORMError(\n                `.whereEntity method can only be used on queries which update real entity table.`,\n            )\n\n        this.expressionMap.wheres = []\n        const entities: Entity[] = Array.isArray(entity) ? entity : [entity]\n        entities.forEach((entity) => {\n            const entityIdMap =\n                this.expressionMap.mainAlias!.metadata.getEntityIdMap(entity)\n            if (!entityIdMap)\n                throw new TypeORMError(\n                    `Provided entity does not have ids set, cannot perform operation.`,\n                )\n\n            this.orWhereInIds(entityIdMap)\n        })\n\n        this.expressionMap.whereEntities = entities\n        return this\n    }\n\n    /**\n     * Indicates if entity must be updated after update operation.\n     * This may produce extra query or use RETURNING / OUTPUT statement (depend on database).\n     * Enabled by default.\n     */\n    updateEntity(enabled: boolean): this {\n        this.expressionMap.updateEntity = enabled\n        return this\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates UPDATE express used to perform insert query.\n     */\n    protected createUpdateExpression() {\n        const valuesSet = this.getValueSet()\n        const metadata = this.expressionMap.mainAlias!.hasMetadata\n            ? this.expressionMap.mainAlias!.metadata\n            : undefined\n\n        // it doesn't make sense to update undefined properties, so just skip them\n        const valuesSetNormalized: ObjectLiteral = {}\n        for (const key in valuesSet) {\n            if (valuesSet[key] !== undefined) {\n                valuesSetNormalized[key] = valuesSet[key]\n            }\n        }\n\n        // prepare columns and values to be updated\n        const updateColumnAndValues: string[] = []\n        const updatedColumns: ColumnMetadata[] = []\n        if (metadata) {\n            this.createPropertyPath(metadata, valuesSetNormalized).forEach(\n                (propertyPath) => {\n                    // todo: make this and other query builder to work with properly with tables without metadata\n                    const columns =\n                        metadata.findColumnsWithPropertyPath(propertyPath)\n\n                    if (columns.length <= 0) {\n                        throw new EntityPropertyNotFoundError(\n                            propertyPath,\n                            metadata,\n                        )\n                    }\n\n                    columns.forEach((column) => {\n                        if (\n                            !column.isUpdate ||\n                            updatedColumns.includes(column)\n                        ) {\n                            return\n                        }\n\n                        updatedColumns.push(column)\n\n                        //\n                        let value = column.getEntityValue(valuesSetNormalized)\n                        if (\n                            column.referencedColumn &&\n                            typeof value === \"object\" &&\n                            !(value instanceof Date) &&\n                            value !== null &&\n                            !Buffer.isBuffer(value)\n                        ) {\n                            value =\n                                column.referencedColumn.getEntityValue(value)\n                        } else if (!(typeof value === \"function\")) {\n                            value =\n                                this.connection.driver.preparePersistentValue(\n                                    value,\n                                    column,\n                                )\n                        }\n\n                        // todo: duplication zone\n                        if (typeof value === \"function\") {\n                            // support for SQL expressions in update query\n                            updateColumnAndValues.push(\n                                this.escape(column.databaseName) +\n                                    \" = \" +\n                                    value(),\n                            )\n                        } else if (\n                            (this.connection.driver.options.type === \"sap\" ||\n                                this.connection.driver.options.type ===\n                                    \"spanner\") &&\n                            value === null\n                        ) {\n                            updateColumnAndValues.push(\n                                this.escape(column.databaseName) + \" = NULL\",\n                            )\n                        } else {\n                            if (\n                                this.connection.driver.options.type === \"mssql\"\n                            ) {\n                                value = (\n                                    this.connection.driver as SqlServerDriver\n                                ).parametrizeValue(column, value)\n                            }\n\n                            const paramName = this.createParameter(value)\n\n                            let expression = null\n                            if (\n                                (DriverUtils.isMySQLFamily(\n                                    this.connection.driver,\n                                ) ||\n                                    this.connection.driver.options.type ===\n                                        \"aurora-mysql\") &&\n                                this.connection.driver.spatialTypes.indexOf(\n                                    column.type,\n                                ) !== -1\n                            ) {\n                                const useLegacy = (\n                                    this.connection.driver as\n                                        | MysqlDriver\n                                        | AuroraMysqlDriver\n                                ).options.legacySpatialSupport\n                                const geomFromText = useLegacy\n                                    ? \"GeomFromText\"\n                                    : \"ST_GeomFromText\"\n                                if (column.srid != null) {\n                                    expression = `${geomFromText}(${paramName}, ${column.srid})`\n                                } else {\n                                    expression = `${geomFromText}(${paramName})`\n                                }\n                            } else if (\n                                DriverUtils.isPostgresFamily(\n                                    this.connection.driver,\n                                ) &&\n                                this.connection.driver.spatialTypes.indexOf(\n                                    column.type,\n                                ) !== -1\n                            ) {\n                                if (column.srid != null) {\n                                    expression = `ST_SetSRID(ST_GeomFromGeoJSON(${paramName}), ${column.srid})::${column.type}`\n                                } else {\n                                    expression = `ST_GeomFromGeoJSON(${paramName})::${column.type}`\n                                }\n                            } else if (\n                                this.connection.driver.options.type ===\n                                    \"mssql\" &&\n                                this.connection.driver.spatialTypes.indexOf(\n                                    column.type,\n                                ) !== -1\n                            ) {\n                                expression =\n                                    column.type +\n                                    \"::STGeomFromText(\" +\n                                    paramName +\n                                    \", \" +\n                                    (column.srid || \"0\") +\n                                    \")\"\n                            } else {\n                                expression = paramName\n                            }\n                            updateColumnAndValues.push(\n                                this.escape(column.databaseName) +\n                                    \" = \" +\n                                    expression,\n                            )\n                        }\n                    })\n                },\n            )\n\n            // Don't allow calling update only with columns that are `update: false`\n            if (\n                updateColumnAndValues.length > 0 ||\n                Object.keys(valuesSetNormalized).length === 0\n            ) {\n                if (\n                    metadata.versionColumn &&\n                    updatedColumns.indexOf(metadata.versionColumn) === -1\n                )\n                    updateColumnAndValues.push(\n                        this.escape(metadata.versionColumn.databaseName) +\n                            \" = \" +\n                            this.escape(metadata.versionColumn.databaseName) +\n                            \" + 1\",\n                    )\n                if (\n                    metadata.updateDateColumn &&\n                    updatedColumns.indexOf(metadata.updateDateColumn) === -1\n                )\n                    updateColumnAndValues.push(\n                        this.escape(metadata.updateDateColumn.databaseName) +\n                            \" = CURRENT_TIMESTAMP\",\n                    ) // todo: fix issue with CURRENT_TIMESTAMP(6) being used, can \"DEFAULT\" be used?!\n            }\n        } else {\n            Object.keys(valuesSetNormalized).map((key) => {\n                const value = valuesSetNormalized[key]\n\n                // todo: duplication zone\n                if (typeof value === \"function\") {\n                    // support for SQL expressions in update query\n                    updateColumnAndValues.push(\n                        this.escape(key) + \" = \" + value(),\n                    )\n                } else if (\n                    (this.connection.driver.options.type === \"sap\" ||\n                        this.connection.driver.options.type === \"spanner\") &&\n                    value === null\n                ) {\n                    updateColumnAndValues.push(this.escape(key) + \" = NULL\")\n                } else {\n                    // we need to store array values in a special class to make sure parameter replacement will work correctly\n                    // if (value instanceof Array)\n                    //     value = new ArrayParameter(value);\n\n                    const paramName = this.createParameter(value)\n                    updateColumnAndValues.push(\n                        this.escape(key) + \" = \" + paramName,\n                    )\n                }\n            })\n        }\n\n        if (updateColumnAndValues.length <= 0) {\n            throw new UpdateValuesMissingError()\n        }\n\n        // get a table name and all column database names\n        const whereExpression = this.createWhereExpression()\n        const returningExpression = this.createReturningExpression(\"update\")\n\n        if (returningExpression === \"\") {\n            return `UPDATE ${this.getTableName(\n                this.getMainTableName(),\n            )} SET ${updateColumnAndValues.join(\", \")}${whereExpression}` // todo: how do we replace aliases in where to nothing?\n        }\n        if (this.connection.driver.options.type === \"mssql\") {\n            return `UPDATE ${this.getTableName(\n                this.getMainTableName(),\n            )} SET ${updateColumnAndValues.join(\n                \", \",\n            )} OUTPUT ${returningExpression}${whereExpression}`\n        }\n        if (this.connection.driver.options.type === \"spanner\") {\n            return `UPDATE ${this.getTableName(\n                this.getMainTableName(),\n            )} SET ${updateColumnAndValues.join(\n                \", \",\n            )}${whereExpression} THEN RETURN ${returningExpression}`\n        }\n\n        return `UPDATE ${this.getTableName(\n            this.getMainTableName(),\n        )} SET ${updateColumnAndValues.join(\n            \", \",\n        )}${whereExpression} RETURNING ${returningExpression}`\n    }\n\n    /**\n     * Creates \"ORDER BY\" part of SQL query.\n     */\n    protected createOrderByExpression() {\n        const orderBys = this.expressionMap.orderBys\n        if (Object.keys(orderBys).length > 0)\n            return (\n                \" ORDER BY \" +\n                Object.keys(orderBys)\n                    .map((columnName) => {\n                        if (typeof orderBys[columnName] === \"string\") {\n                            return (\n                                this.replacePropertyNames(columnName) +\n                                \" \" +\n                                orderBys[columnName]\n                            )\n                        } else {\n                            return (\n                                this.replacePropertyNames(columnName) +\n                                \" \" +\n                                (orderBys[columnName] as any).order +\n                                \" \" +\n                                (orderBys[columnName] as any).nulls\n                            )\n                        }\n                    })\n                    .join(\", \")\n            )\n\n        return \"\"\n    }\n\n    /**\n     * Creates \"LIMIT\" parts of SQL query.\n     */\n    protected createLimitExpression(): string {\n        const limit: number | undefined = this.expressionMap.limit\n\n        if (limit) {\n            if (\n                DriverUtils.isMySQLFamily(this.connection.driver) ||\n                this.connection.driver.options.type === \"aurora-mysql\"\n            ) {\n                return \" LIMIT \" + limit\n            } else {\n                throw new LimitOnUpdateNotSupportedError()\n            }\n        }\n\n        return \"\"\n    }\n\n    /**\n     * Gets array of values need to be inserted into the target table.\n     */\n    protected getValueSet(): ObjectLiteral {\n        if (typeof this.expressionMap.valuesSet === \"object\")\n            return this.expressionMap.valuesSet\n\n        throw new UpdateValuesMissingError()\n    }\n}\n"], "sourceRoot": ".."}