# Docker Setup for Micro Frontend Architecture

This directory contains all Docker-related configurations for the micro frontend application.

## 📁 Directory Structure

```
docker/
├── Dockerfile                 # Multi-stage build for all services
├── shell.Dockerfile          # Shell application specific build
├── auth.Dockerfile           # Auth micro frontend specific build
├── docker-compose.yml        # Production orchestration
├── docker-compose.dev.yml    # Development with hot reload
├── .dockerignore             # Docker build context optimization
├── nginx/                    # Nginx configurations
│   ├── shell.conf           # Shell application nginx config
│   └── auth.conf            # Auth micro frontend nginx config
└── README.md                 # This file
```

## 🏗️ Architecture Overview

The application consists of:
- **Shell Application** (Port 3000) - Main container and router
- **Auth Micro Frontend** (Port 3001) - Authentication flows
- **Dashboard Micro Frontend** (Port 3002) - Dashboard features
- **Profile Micro Frontend** (Port 3003) - User profile management
- **Traefik** (Port 80, 8080) - Reverse proxy and load balancer

## 🚀 Quick Start

### Production Mode

```bash
# From the docker directory
cd docker
docker-compose up -d

# Or from the root directory
npm run docker:up
```

### Development Mode

```bash
# From the docker directory
cd docker
docker-compose -f docker-compose.dev.yml up -d

# Or from the root directory
npm run docker:dev
```

## 📋 Available Commands (from root directory)

### Docker Operations
```bash
npm run docker:build      # Build all Docker images
npm run docker:up          # Start production containers
npm run docker:down        # Stop and remove containers
npm run docker:logs        # View container logs
npm run docker:clean       # Clean up everything (images, volumes, networks)
npm run docker:rebuild     # Clean and rebuild everything
```

### Development Operations
```bash
npm run docker:dev         # Start development containers
npm run docker:dev:down    # Stop development containers
npm run docker:dev:logs    # View development logs
```

## 🌐 Service URLs

### Production Mode
- **Shell Application**: http://localhost:3000
- **Auth Micro Frontend**: http://localhost:3001
- **Dashboard**: http://localhost:3002
- **Profile**: http://localhost:3003
- **Traefik Dashboard**: http://localhost:8080

### Development Mode
- **Shell Application**: http://localhost:3000 (with hot reload)
- **Auth Micro Frontend**: http://localhost:3001 (with hot reload)
- **Dashboard**: http://localhost:3002 (with hot reload)
- **Profile**: http://localhost:3003 (with hot reload)

## 🔧 Configuration

### Environment Variables

Create `.env` files for different environments:

```bash
# .env.production
NODE_ENV=production
VITE_AUTH_URL=http://localhost:3001
VITE_DASHBOARD_URL=http://localhost:3002
VITE_PROFILE_URL=http://localhost:3003

# .env.development
NODE_ENV=development
VITE_AUTH_URL=http://localhost:3001
VITE_DASHBOARD_URL=http://localhost:3002
VITE_PROFILE_URL=http://localhost:3003
```

### Docker Compose Overrides

Create `docker-compose.override.yml` for local customizations:

```yaml
version: '3.8'
services:
  shell:
    environment:
      - CUSTOM_VAR=value
    ports:
      - "3000:3000"
```

## 🐛 Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check what's using the ports
   lsof -i :3000
   lsof -i :3001
   
   # Kill processes if needed
   kill -9 <PID>
   ```

2. **Build failures**
   ```bash
   # Clean everything and rebuild
   npm run docker:clean
   npm run docker:rebuild
   ```

3. **Module Federation issues**
   ```bash
   # Check if remoteEntry.js is accessible
   curl http://localhost:3001/remoteEntry.js
   ```

4. **Network issues**
   ```bash
   # Inspect Docker networks
   docker network ls
   docker network inspect app-front-network
   ```

### Debugging

```bash
# Enter a running container
docker exec -it app-front-shell sh
docker exec -it app-front-auth sh

# View container details
docker inspect app-front-shell

# Check container health
docker ps
```

## 📊 Monitoring

### Health Checks

All services include health check endpoints:
- Shell: http://localhost:3000/health
- Auth: http://localhost:3001/health

### Logs

```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f shell
docker-compose logs -f auth

# View last 100 lines
docker-compose logs --tail=100 shell
```

## 🔒 Security

### Production Considerations

1. **Environment Variables**: Use Docker secrets for sensitive data
2. **Network Security**: Configure proper firewall rules
3. **SSL/TLS**: Add HTTPS termination at load balancer
4. **Image Security**: Regularly update base images

### Security Headers

Nginx configurations include security headers:
- X-Frame-Options
- X-XSS-Protection
- X-Content-Type-Options
- Content-Security-Policy

## 🚀 Deployment

### CI/CD Pipeline

```yaml
# Example GitHub Actions workflow
name: Deploy
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build and Deploy
        run: |
          cd docker
          docker-compose build
          docker-compose up -d
```

### Production Deployment

1. **Build images**: `npm run docker:build`
2. **Push to registry**: `docker push your-registry/app-front-shell`
3. **Deploy**: `npm run docker:up`
4. **Health check**: Verify all services are healthy

## 📈 Performance

### Optimization Tips

1. **Multi-stage builds**: Reduce image sizes
2. **Layer caching**: Optimize Dockerfile layer order
3. **Nginx caching**: Configure proper cache headers
4. **Gzip compression**: Enable for static assets
5. **CDN**: Use CDN for static assets in production

### Monitoring

- Use Traefik dashboard for traffic monitoring
- Implement application metrics with Prometheus
- Set up log aggregation with ELK stack
