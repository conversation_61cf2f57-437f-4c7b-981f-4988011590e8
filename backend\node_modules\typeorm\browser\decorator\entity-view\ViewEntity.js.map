{"version": 3, "sources": ["../browser/src/decorator/entity-view/ViewEntity.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AAGtD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAA;AAiBpD;;;GAGG;AACH,MAAM,UAAU,UAAU,CACtB,aAA0C,EAC1C,YAAgC;IAEhC,MAAM,OAAO,GACT,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC;QAChC,CAAC,CAAE,aAAmC;QACtC,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;IAC7B,MAAM,IAAI,GACN,OAAO,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAA;IAEpE,OAAO,UAAU,MAAgB;QAC7B,sBAAsB,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;YACjC,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,IAAI;YACV,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,SAAS,EAAE,OAAO,CAAC,SAAS;gBACxB,CAAC,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC;gBAC5B,CAAC,CAAC,SAAS;YACf,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;YACzD,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YACnD,WAAW,EAAE,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;YACzD,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY;SAClB,CAAC,CAAA;IAC3B,CAAC,CAAA;AACL,CAAC", "file": "ViewEntity.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { TableMetadataArgs } from \"../../metadata-args/TableMetadataArgs\"\nimport { ViewEntityOptions } from \"../options/ViewEntityOptions\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\n\n/**\n * This decorator is used to mark classes that will be an entity view.\n * Database schema will be created for all classes decorated with it, and Repository can be retrieved and used for it.\n */\nexport function ViewEntity(options?: ViewEntityOptions): ClassDecorator\n\n/**\n * This decorator is used to mark classes that will be an entity view.\n * Database schema will be created for all classes decorated with it, and Repository can be retrieved and used for it.\n */\nexport function ViewEntity(\n    name?: string,\n    options?: ViewEntityOptions,\n): ClassDecorator\n\n/**\n * This decorator is used to mark classes that will be an entity view.\n * Database schema will be created for all classes decorated with it, and Repository can be retrieved and used for it.\n */\nexport function ViewEntity(\n    nameOrOptions?: string | ViewEntityOptions,\n    maybeOptions?: ViewEntityOptions,\n): ClassDecorator {\n    const options =\n        (ObjectUtils.isObject(nameOrOptions)\n            ? (nameOrOptions as ViewEntityOptions)\n            : maybeOptions) || {}\n    const name =\n        typeof nameOrOptions === \"string\" ? nameOrOptions : options.name\n\n    return function (target: Function) {\n        getMetadataArgsStorage().tables.push({\n            target: target,\n            name: name,\n            expression: options.expression,\n            dependsOn: options.dependsOn\n                ? new Set(options.dependsOn)\n                : undefined,\n            type: \"view\",\n            database: options.database ? options.database : undefined,\n            schema: options.schema ? options.schema : undefined,\n            synchronize: options.synchronize === false ? false : true,\n            materialized: !!options.materialized,\n        } as TableMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}