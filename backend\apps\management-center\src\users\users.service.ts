import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { User, UserDocument, UserStatus } from '../auth/schemas/user.schema';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { PaginationDto } from '@enterprise/common';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name, 'write')
    private userWriteModel: Model<UserDocument>,
    @InjectModel(User.name, 'read')
    private userReadModel: Model<UserDocument>,
  ) {}

  async findAll(pagination?: PaginationDto): Promise<{ users: UserDocument[]; total: number }> {
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 10;
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      this.userReadModel
        .find({ deletedAt: { $exists: false } })
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.userReadModel.countDocuments({ deletedAt: { $exists: false } }).exec(),
    ]);

    return { users, total };
  }

  async findOne(id: string): Promise<UserDocument> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid user ID format');
    }

    const user = await this.userReadModel
      .findOne({ _id: id, deletedAt: { $exists: false } })
      .exec();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findByEmail(email: string): Promise<UserDocument | null> {
    return this.userReadModel
      .findOne({ email, deletedAt: { $exists: false } })
      .exec();
  }

  async findByOrganization(organizationId: string, pagination?: PaginationDto): Promise<{ users: UserDocument[]; total: number }> {
    if (!Types.ObjectId.isValid(organizationId)) {
      throw new NotFoundException('Invalid organization ID format');
    }

    const page = pagination?.page || 1;
    const limit = pagination?.limit || 10;
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      this.userReadModel
        .find({ 
          organizationId: new Types.ObjectId(organizationId),
          deletedAt: { $exists: false }
        })
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.userReadModel.countDocuments({ 
        organizationId: new Types.ObjectId(organizationId),
        deletedAt: { $exists: false }
      }).exec(),
    ]);

    return { users, total };
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<UserDocument> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid user ID format');
    }

    const user = await this.userWriteModel
      .findOneAndUpdate(
        { _id: id, deletedAt: { $exists: false } },
        { 
          ...updateUserDto,
          updatedAt: new Date(),
        },
        { new: true }
      )
      .exec();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async updateStatus(id: string, status: UserStatus): Promise<UserDocument> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid user ID format');
    }

    const user = await this.userWriteModel
      .findOneAndUpdate(
        { _id: id, deletedAt: { $exists: false } },
        { 
          status,
          updatedAt: new Date(),
        },
        { new: true }
      )
      .exec();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async verifyEmail(id: string): Promise<UserDocument> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid user ID format');
    }

    const user = await this.userWriteModel
      .findOneAndUpdate(
        { _id: id, deletedAt: { $exists: false } },
        { 
          emailVerified: true,
          status: UserStatus.ACTIVE,
          emailVerificationToken: undefined,
          emailVerificationExpires: undefined,
          updatedAt: new Date(),
        },
        { new: true }
      )
      .exec();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async remove(id: string): Promise<void> {
    if (!Types.ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid user ID format');
    }

    const result = await this.userWriteModel
      .findOneAndUpdate(
        { _id: id, deletedAt: { $exists: false } },
        { 
          deletedAt: new Date(),
          updatedAt: new Date(),
        }
      )
      .exec();

    if (!result) {
      throw new NotFoundException('User not found');
    }
  }

  async getActiveUsersCount(): Promise<number> {
    return this.userReadModel.countDocuments({ 
      status: UserStatus.ACTIVE,
      deletedAt: { $exists: false }
    }).exec();
  }

  async getUsersByRole(role: string): Promise<UserDocument[]> {
    return this.userReadModel
      .find({ 
        role,
        deletedAt: { $exists: false }
      })
      .sort({ createdAt: -1 })
      .exec();
  }

  async searchUsers(query: string, pagination?: PaginationDto): Promise<{ users: UserDocument[]; total: number }> {
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 10;
    const skip = (page - 1) * limit;

    const searchRegex = new RegExp(query, 'i');
    const searchFilter = {
      $or: [
        { firstName: searchRegex },
        { lastName: searchRegex },
        { email: searchRegex },
      ],
      deletedAt: { $exists: false }
    };

    const [users, total] = await Promise.all([
      this.userReadModel
        .find(searchFilter)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.userReadModel.countDocuments(searchFilter).exec(),
    ]);

    return { users, total };
  }
}
